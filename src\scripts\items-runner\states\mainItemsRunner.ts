import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Inventory } from '../../../api/game/inventory'
import { Walking } from '../../../api/game/walking'
import { WorldHopping } from '../../../api/game/worldHopping'
import { ItemId } from '../../../data/itemId'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { GeAction } from '../../../api/game/geAction'
import { ResupplyMuleState } from '../../../api/script-utils/mule/resupplyMuleStrategy'
import { GiveToMuleState } from '../../../api/script-utils/mule/giveMuleStrategy'
import { Item } from '../../../api/model/item'
import { TradePackage } from '../../../api/model/tradePackage'
import { BotSettings } from '../../../botSettings'
import { MuleReceiver } from '../../muling/muleReceiver'
import { addEquipmentManager } from '../../../api/script-utils/states/equipmentStates'
import { GetRingOfTheElementsState } from '../../mud-crafter/states/mudCrafterGe'
import { Teleport } from '../../../data/teleport'
import { Equipment } from '../../../api/game/equipment'
import { Varps } from '../../../api/game/varps'
import { ItemPredicate } from '../../../data/itemPredicates'
import { Player } from '../../../api/wrappers/player'
import { Time } from '../../../api/utils/time'
import { Tile } from '../../../api/model/tile'
import { GameObjects } from '../../../api/game/gameObjects'
import { Redis } from '../../../api/utils/redis'

export type ItemsRunnerSpot = 'pure-essence'


export class MainItemsRunner extends State {

    resupply = new ResupplyMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_RESUPPLY_2m), 308)
    giveToMule = new GiveToMuleState(TradePackage.forAccount(MuleReceiver.PACKAGE_GIVE), BotSettings.tradeWorldP2p, [new Item(995, 700_000)], [new Item(995, ***********)])

    onDraw(canvas: any, paint: any): void {
        const task = Redis.get("delivery:tasks:mud-runes:123456")
        this.drawText(`Items runner (${this.currentState?.name})`)
    }

    get spot() {
        return "pure-essence"
    }

    onFirstExecute(): void {
        addEquipmentManager(this, [])
    }

    onBackgroundAction(): void {
        Walking.setRunAuto()
    }

    onAction(): void {
        if (!WorldHopping.switchToP2pExcept()) {
            return
        }

        this.setState(this.bankingState)
    }

    bankingState = createState('Banking', () => {
        Walking.setRunAuto()

        if (Player.getRunEnergy() < 20) {
            this.setState(this.drinkStamina)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        // Deposit all except what we need
        Bank.depositAllExceptPredicate((i) =>
            [ItemId.RING_OF_THE_ELEMENTS_26818, ItemId.PURE_ESSENCE].includes(i.id) ||
            Teleport.ringOfDuelingPredicate(i)
        )

        // Equip earth tiara (needed to enter mud altar)
        if (!Equipment.withdrawAndEquip(ItemId.EARTH_TIARA, () => this.geState)) {
            return
        }

        // Equip ring of dueling
        if (!Equipment.withdrawAndEquipByPredicate(Teleport.ringOfDuelingPredicate, () => this.geState)) {
            return
        }

        // Withdraw ring of elements
        if (!Withdraw.id(ItemId.RING_OF_THE_ELEMENTS_26818, 1).minimumAmount(1).orState(() => new GetRingOfTheElementsState(this, this.resupply)).ensureSpace().withdraw()) {
            return
        }

        // Withdraw pure essence
        if (!Withdraw.id(ItemId.PURE_ESSENCE, 25).minimumAmount(25).orState(() => this.geState).ensureSpace().withdraw()) {
            return
        }

        this.setState(this.deliveryState)
    })

    drinkStamina = createState('Drinking stamina potion', () => {
        if (Player.getRunEnergy() > 80 && Player.isStaminaPotionActive()) {
            this.setState(this.bankingState)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        if (!Withdraw.predicate(ItemPredicate.staminaPotion, 1).orState(() => this.geState).withdraw()) {
            return
        }

        if (Bank.isOpen()) {
            Inventory.get(Inventory.bank).getByPredicate(ItemPredicate.staminaPotion)?.click(1007, 9)
        } else {
            Inventory.getByPredicate(ItemPredicate.staminaPotion)?.click(57, 2)
        }

        Time.sleep(400, 1000)
    })

    deliveryState = createState('Delivering pure essence', () => {
        // If no pure essence left, go back to banking
        if (!Inventory.contains(ItemId.PURE_ESSENCE)) {
            this.setState(this.bankingState)
            return
        }

        if (new Tile(2658, 4839, 0).distance() < 20) {
            if(!Walking.walkTo(new Tile(2656, 4839, 0), 1)) return

            return
        }

        // Check if we're near the earth altar entrance
        if (new Tile(3308, 3474, 0).distance() > 30) {
            // Use ring of elements to teleport to earth altar
            Teleport.ringOfTheElementsEarth.process()
            return
        } else {
            // Walk to the earth altar entrance and enter it
            const obj = GameObjects.getNearest(new Tile(3308, 3474, 0), (o) => o.realId == 29099, 10)
            if (obj == null && !Walking.walkTo(new Tile(3308, 3474, 0), 6)) return

            obj?.click(3)
            Time.sleep(() => new Tile(2658, 4839, 0).distance() < 30)
        }
    })

    geState = createGeState(
        () => this,
        () => this.resupply,
        [
            GeAction.item(ItemId.PURE_ESSENCE, 3000).gePrice(1.12, 5).buy(),
            GeAction.item(ItemId.RING_OF_DUELING8, 40).gePrice(1.12, 1000).buy(),
            GeAction.item(ItemId.STAMINA_POTION4, 15).gePrice(1.1, 1000).buy(),
            GeAction.item(ItemId.EARTH_TIARA, 1).gePrice(1.12, 1555).withEq().buy(),
        ], 'Items Runner GE'
    )
}
