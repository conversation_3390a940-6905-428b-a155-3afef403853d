import { javaPerform, log } from './utils'

/**
 * Redis client wrapper that uses Frida to call the Java layer Redis instance
 * Uses the redisSync instance from com.szczawicznecrew.osrsbot.Bootstrapper
 */
export class Redis {
    private static bootstrapperClass: any = null

    /**
     * Initialize the Redis wrapper by getting reference to the Java Bootstrapper class
     */
    private static init() {
        if (this.bootstrapperClass === null) {
            javaPerform(() => {
                try {
                    this.bootstrapperClass = Java.use('com.szczawicznecrew.osrsbot.Bootstrapper')
                    log('[Redis] Initialized Redis wrapper')
                } catch (e) {
                    log('[Redis] Failed to initialize Redis wrapper:', e)
                    throw e
                }
            })
        }
    }

    /**
     * Get the redisSync instance from the Java layer
     */
    private static getRedisSync(): any {
        this.init()
        return this.bootstrapperClass.redisSync.value
    }

    /**
     * Set a key-value pair in Redis
     * @param key The key to set
     * @param value The value to set
     * @param ttlSeconds Optional TTL in seconds
     * @returns Promise<string> The result of the SET operation
     */
    static set(key: string, value: string, ttlSeconds?: number): string {
        let result: string = null

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()

                if (ttlSeconds !== undefined) {
                    // Use SET with EX option for TTL
                    const SetArgs = Java.use('io.lettuce.core.SetArgs')
                    const setArgs = SetArgs.$new().ex(ttlSeconds)
                    result = redisSync.set(key, value, setArgs)
                } else {
                    // Simple SET without TTL
                    result = redisSync.set(key, value)
                }

            } catch (e) {
                log('[Redis] Error in set operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Get a value from Redis by key
     * @param key The key to get
     * @returns string | null The value or null if key doesn't exist
     */
    static get(key: string, debounceTime: number = 100): string | null {
        let result: string | null = null

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                result = redisSync.get(key)
            } catch (e) {
                log('[Redis] Error in get operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Delete a key from Redis
     * @param key The key to delete
     * @returns number The number of keys deleted (0 or 1)
     */
    static del(key: string): number {
        let result: number = 0

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                result = redisSync.del(key)
                log(`[Redis] DEL ${key} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in del operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Check if a key exists in Redis
     * @param key The key to check
     * @returns boolean True if key exists, false otherwise
     */
    static exists(key: string): boolean {
        let result: boolean = false

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                const count = redisSync.exists(key)
                result = count > 0
                log(`[Redis] EXISTS ${key} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in exists operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Set TTL (time to live) for a key
     * @param key The key to set TTL for
     * @param seconds TTL in seconds
     * @returns boolean True if TTL was set, false if key doesn't exist
     */
    static expire(key: string, seconds: number): boolean {
        let result: boolean = false

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                result = redisSync.expire(key, seconds)
                log(`[Redis] EXPIRE ${key} ${seconds} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in expire operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Get TTL (time to live) for a key
     * @param key The key to get TTL for
     * @returns number TTL in seconds, -1 if key exists but has no TTL, -2 if key doesn't exist
     */
    static ttl(key: string): number {
        let result: number = -2

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                result = redisSync.ttl(key)
                log(`[Redis] TTL ${key} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in ttl operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Increment a numeric value stored at key
     * @param key The key to increment
     * @param amount Optional amount to increment by (default: 1)
     * @returns number The new value after increment
     */
    static incr(key: string, amount: number = 1): number {
        let result: number = 0

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                if (amount === 1) {
                    result = redisSync.incr(key)
                } else {
                    result = redisSync.incrby(key, amount)
                }
                log(`[Redis] INCR ${key} by ${amount} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in incr operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Decrement a numeric value stored at key
     * @param key The key to decrement
     * @param amount Optional amount to decrement by (default: 1)
     * @returns number The new value after decrement
     */
    static decr(key: string, amount: number = 1): number {
        let result: number = 0

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                if (amount === 1) {
                    result = redisSync.decr(key)
                } else {
                    result = redisSync.decrby(key, amount)
                }
                log(`[Redis] DECR ${key} by ${amount} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in decr operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Set a key only if it doesn't exist
     * @param key The key to set
     * @param value The value to set
     * @param ttlSeconds Optional TTL in seconds
     * @returns boolean True if key was set, false if key already exists
     */
    static setnx(key: string, value: string, ttlSeconds?: number): boolean {
        let result: boolean = false

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()

                if (ttlSeconds !== undefined) {
                    const SetArgs = Java.use('io.lettuce.core.SetArgs')
                    const setArgs = SetArgs.$new().nx().ex(ttlSeconds)
                    const setResult = redisSync.set(key, value, setArgs)
                    result = setResult !== null && setResult.equals('OK')
                } else {
                    result = redisSync.setnx(key, value)
                }

                log(`[Redis] SETNX ${key} = ${value}${ttlSeconds ? ` (TTL: ${ttlSeconds}s)` : ''} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in setnx operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Get multiple keys at once
     * @param keys Array of keys to get
     * @returns Array of values (null for non-existent keys)
     */
    static mget(keys: string[]): (string | null)[] {
        let result: (string | null)[] = []

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                const javaArray = Java.array('java.lang.String', keys)
                const values = redisSync.mget(javaArray)

                // Convert Java List to JavaScript array
                result = []
                for (let i = 0; i < values.size(); i++) {
                    const value = values.get(i)
                    result.push(value ? value.getValue() : null)
                }

                log(`[Redis] MGET [${keys.join(', ')}] -> [${result.join(', ')}]`)
            } catch (e) {
                log('[Redis] Error in mget operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Set multiple key-value pairs at once
     * @param keyValues Object with key-value pairs
     * @returns string Result of the operation
     */
    static mset(keyValues: Record<string, string>): string {
        let result: string = null

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                const HashMap = Java.use('java.util.HashMap')
                const map = HashMap.$new()

                Object.entries(keyValues).forEach(([key, value]) => {
                    map.put(key, value)
                })

                result = redisSync.mset(map)
                log(`[Redis] MSET ${Object.keys(keyValues).length} keys -> ${result}`)
            } catch (e) {
                log('[Redis] Error in mset operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Append a value to an existing string key
     * @param key The key to append to
     * @param value The value to append
     * @returns number The length of the string after append
     */
    static append(key: string, value: string): number {
        let result: number = 0

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                result = redisSync.append(key, value)
                log(`[Redis] APPEND ${key} += "${value}" -> length: ${result}`)
            } catch (e) {
                log('[Redis] Error in append operation:', e)
                throw e
            }
        })

        return result
    }

    /**
     * Get the length of a string value
     * @param key The key to get length for
     * @returns number The length of the string
     */
    static strlen(key: string): number {
        let result: number = 0

        javaPerform(() => {
            try {
                const redisSync = this.getRedisSync()
                result = redisSync.strlen(key)
                log(`[Redis] STRLEN ${key} -> ${result}`)
            } catch (e) {
                log('[Redis] Error in strlen operation:', e)
                throw e
            }
        })

        return result
    }

        /**
     * Get a list of keys matching a pattern
     * @param pattern The pattern to match keys against (use * for wildcard, ? for single character)
     * @returns string[] Array of keys matching the pattern
     */
    static keys(pattern: string = '*'): string[] {
        let result: string[] = []

        javaPerform(() => {
            try {
                result = this.bootstrapperClass.keys(pattern)
            } catch (e) {
                log('[Redis] Error in keys operation:', e)
                throw e
            }
        })

        return result
    }
}